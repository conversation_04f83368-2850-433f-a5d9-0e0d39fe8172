#!/usr/bin/env python3
"""
Test script to verify device selection functionality
"""

import torch
from auto_variation_finder import ImageVariationFinder

def test_device_selection():
    """Test different device selection options"""
    
    print("Testing Device Selection Functionality")
    print("=" * 40)
    
    # Check available devices
    print("\nAvailable devices:")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print(f"MPS available: {torch.backends.mps.is_available()}")
    print(f"CPU available: True (always)")
    
    # Test different device options
    device_options = ["auto", "cpu", "gpu", "mps", "cuda"]
    
    for device in device_options:
        print(f"\nTesting device: {device}")
        try:
            finder = ImageVariationFinder(
                clip_threshold=0.85,
                clip_model_name="ViT-B/32",
                device=device
            )
            print(f"✓ Successfully initialized with device: {finder.device}")
            
            # Clean up
            del finder
            
        except Exception as e:
            print(f"✗ Failed to initialize with device '{device}': {e}")
    
    print("\nDevice selection test completed!")

if __name__ == "__main__":
    test_device_selection()