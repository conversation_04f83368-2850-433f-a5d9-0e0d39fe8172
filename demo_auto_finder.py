#!/usr/bin/env python3
"""
Demo script for automatic image variation finder

This script creates sample images with variations and demonstrates
the automatic detection and batching functionality.
"""

import os
import shutil
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
from auto_variation_finder import ImageVariationFinder
import random

def create_demo_images():
    """
    Create sample images with variations for demonstration.
    """
    demo_dir = 'demo_images'
    
    # Clean up existing demo directory
    if os.path.exists(demo_dir):
        shutil.rmtree(demo_dir)
    
    os.makedirs(demo_dir)
    
    print("Creating demo images with variations...")
    
    # Create base images
    base_images = [
        {'name': 'landscape', 'color': '#4CAF50', 'shape': 'rectangle'},
        {'name': 'portrait', 'color': '#2196F3', 'shape': 'circle'},
        {'name': 'abstract', 'color': '#FF9800', 'shape': 'triangle'},
        {'name': 'geometric', 'color': '#9C27B0', 'shape': 'diamond'}
    ]
    
    created_files = []
    
    for base in base_images:
        # Create original image
        img = create_base_image(base['color'], base['shape'])
        original_path = os.path.join(demo_dir, f"{base['name']}_original.jpg")
        img.save(original_path, quality=95)
        created_files.append(original_path)
        
        # Create variations
        variations = [
            ('brightness', lambda x: ImageEnhance.Brightness(x).enhance(1.3)),
            ('contrast', lambda x: ImageEnhance.Contrast(x).enhance(1.2)),
            ('blur', lambda x: x.filter(ImageFilter.GaussianBlur(radius=1))),
            ('rotated', lambda x: x.rotate(5, expand=True, fillcolor='white'))
        ]
        
        for var_name, transform in variations:
            var_img = transform(img.copy())
            var_path = os.path.join(demo_dir, f"{base['name']}_{var_name}.jpg")
            var_img.save(var_path, quality=95)
            created_files.append(var_path)
    
    # Create some unrelated images
    unrelated = [
        {'name': 'random1', 'color': '#F44336', 'shape': 'star'},
        {'name': 'random2', 'color': '#00BCD4', 'shape': 'hexagon'}
    ]
    
    for item in unrelated:
        img = create_base_image(item['color'], item['shape'])
        path = os.path.join(demo_dir, f"{item['name']}.jpg")
        img.save(path, quality=95)
        created_files.append(path)
    
    print(f"Created {len(created_files)} demo images in '{demo_dir}' directory")
    return demo_dir, created_files

def create_base_image(color: str, shape: str, size: tuple = (400, 300)):
    """
    Create a base image with specified color and shape.
    """
    img = Image.new('RGB', size, 'white')
    draw = ImageDraw.Draw(img)
    
    # Calculate shape bounds
    margin = 50
    left = margin
    top = margin
    right = size[0] - margin
    bottom = size[1] - margin
    center_x = size[0] // 2
    center_y = size[1] // 2
    
    if shape == 'rectangle':
        draw.rectangle([left, top, right, bottom], fill=color)
    elif shape == 'circle':
        radius = min(size[0], size[1]) // 3
        draw.ellipse([center_x - radius, center_y - radius, 
                     center_x + radius, center_y + radius], fill=color)
    elif shape == 'triangle':
        points = [(center_x, top), (left, bottom), (right, bottom)]
        draw.polygon(points, fill=color)
    elif shape == 'diamond':
        points = [(center_x, top), (right, center_y), 
                 (center_x, bottom), (left, center_y)]
        draw.polygon(points, fill=color)
    elif shape == 'star':
        # Create a simple star shape
        outer_radius = min(size[0], size[1]) // 4
        inner_radius = outer_radius // 2
        points = []
        for i in range(10):
            angle = i * 36  # 360/10 = 36 degrees
            if i % 2 == 0:
                radius = outer_radius
            else:
                radius = inner_radius
            x = center_x + radius * (1 if angle == 0 else 
                                   0.951 if angle == 36 else
                                   0.309 if angle == 72 else
                                   -0.309 if angle == 108 else
                                   -0.951 if angle == 144 else
                                   -1 if angle == 180 else
                                   -0.951 if angle == 216 else
                                   -0.309 if angle == 252 else
                                   0.309 if angle == 288 else
                                   0.951)
            y = center_y + radius * (0 if angle == 0 else
                                   0.309 if angle == 36 else
                                   0.951 if angle == 72 else
                                   0.951 if angle == 108 else
                                   0.309 if angle == 144 else
                                   0 if angle == 180 else
                                   -0.309 if angle == 216 else
                                   -0.951 if angle == 252 else
                                   -0.951 if angle == 288 else
                                   -0.309)
            points.append((int(x), int(y)))
        draw.polygon(points, fill=color)
    elif shape == 'hexagon':
        # Create hexagon
        radius = min(size[0], size[1]) // 4
        points = []
        for i in range(6):
            angle = i * 60  # 360/6 = 60 degrees
            x = center_x + radius * (1 if angle == 0 else
                                   0.5 if angle == 60 else
                                   -0.5 if angle == 120 else
                                   -1 if angle == 180 else
                                   -0.5 if angle == 240 else
                                   0.5)
            y = center_y + radius * (0 if angle == 0 else
                                   0.866 if angle == 60 else
                                   0.866 if angle == 120 else
                                   0 if angle == 180 else
                                   -0.866 if angle == 240 else
                                   -0.866)
            points.append((int(x), int(y)))
        draw.polygon(points, fill=color)
    
    return img

def run_demo():
    """
    Run the complete demonstration.
    """
    print("=" * 60)
    print("AUTOMATIC IMAGE VARIATION FINDER - DEMO")
    print("=" * 60)
    
    # Create demo images
    demo_dir, created_files = create_demo_images()
    
    print(f"\nDemo images created:")
    for file in created_files:
        print(f"  - {os.path.basename(file)}")
    
    # Run automatic variation finder
    print("\n" + "=" * 40)
    print("RUNNING AUTOMATIC VARIATION DETECTION")
    print("=" * 40)
    
    finder = ImageVariationFinder(similarity_threshold=15)  # More lenient for demo
    
    try:
        finder.process_directory(
            input_dir=demo_dir,
            output_dir='demo_output',
            batch_size=4
        )
        
        print("\n" + "=" * 40)
        print("DEMO COMPLETE!")
        print("=" * 40)
        print("\nCheck the following directories:")
        print(f"  - Input images: {demo_dir}/")
        print(f"  - Output batches: demo_output/")
        print("\nThe tool automatically:")
        print("  1. Scanned all images in the input directory")
        print("  2. Calculated perceptual hashes for similarity detection")
        print("  3. Grouped similar images together")
        print("  4. Created batches of up to 4 similar images")
        print("  5. Combined each batch into a single composite image")
        print("  6. Generated source file lists for each batch")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        print("Make sure you have installed the required dependencies:")
        print("  pip install -r requirements.txt")

def cleanup_demo():
    """
    Clean up demo files.
    """
    dirs_to_remove = ['demo_images', 'demo_output']
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Cleaned up {dir_name} directory")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'cleanup':
        cleanup_demo()
    else:
        run_demo()
        print("\nTo clean up demo files, run: python demo_auto_finder.py cleanup")