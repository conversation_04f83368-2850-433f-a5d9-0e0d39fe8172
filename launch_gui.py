#!/usr/bin/env python3
"""
Launcher script for the Image Combiner GUI

This script ensures the GUI runs with proper error handling and logging.
"""

import sys
import os
import traceback

def main():
    try:
        # Add the current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Import and run the GUI
        from image_combiner_gui import main as gui_main
        print("Starting Image Combiner GUI...")
        gui_main()
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("\nMake sure you have installed the required dependencies:")
        print("  pip install -r requirements.txt")
        print("\nOr activate the virtual environment:")
        print("  source venv/bin/activate")
        sys.exit(1)
        
    except Exception as e:
        print(f"Error starting GUI: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()