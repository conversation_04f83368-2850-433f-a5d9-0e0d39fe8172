# Enhanced Image Similarity Detection

This enhanced version of the Image Variation Finder now uses multiple advanced computer vision techniques for maximum accuracy in detecting similar images.

## New Features

### 🚀 Multi-Method Similarity Detection

The tool now combines four different similarity detection methods:

1. **CLIP (Semantic Similarity)** - AI-based content understanding
2. **Perceptual Hashing** - Fast structural similarity detection  
3. **ORB Features** - Keypoint matching for geometric similarity
4. **SIFT Features** - Scale-invariant feature matching (optional)

### 🎯 Improved Accuracy

By combining multiple methods, the tool can now detect:
- **Semantic variations**: Same subject, different style/angle (CLIP)
- **Structural variations**: Same layout, different colors (Perceptual Hash)
- **Geometric variations**: Same objects, different scale/rotation (ORB/SIFT)
- **Exact duplicates**: Pixel-perfect or near-perfect matches (All methods)

### ⚙️ Configurable Detection Methods

You can enable/disable individual methods based on your needs:
- **Fast mode**: Perceptual Hash + ORB only
- **Accurate mode**: All methods enabled
- **Semantic mode**: CLIP only for content-based grouping
- **Custom mode**: Choose your own combination

## Installation

### Basic Installation (Perceptual Hash + ORB)
```bash
pip install Pillow numpy opencv-python imagehash
```

### Full Installation (All Methods)
```bash
pip install -r requirements.txt
```

### GPU Support (Recommended for CLIP)
```bash
# For NVIDIA GPUs
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# For Apple Silicon Macs
pip install torch torchvision
```

## Usage

### GUI Application
Run the enhanced GUI with new similarity options:
```bash
python image_combiner_gui.py
```

The GUI now includes:
- Method selection checkboxes
- Individual threshold controls
- Real-time method status display

### Command Line
```bash
# Use all methods (default)
python auto_variation_finder.py input_folder

# Use only perceptual hashing (fastest)
python auto_variation_finder.py input_folder --disable-clip --disable-orb

# Use CLIP + ORB for best semantic + geometric matching
python auto_variation_finder.py input_folder --disable-hash --enable-sift

# Custom thresholds
python auto_variation_finder.py input_folder --clip-threshold 0.9 --hash-threshold 5 --orb-threshold 0.8
```

## Method Comparison

| Method | Speed | Accuracy | Best For |
|--------|-------|----------|----------|
| **Perceptual Hash** | ⚡⚡⚡ | ⭐⭐⭐ | Structural duplicates, resized images |
| **ORB Features** | ⚡⚡ | ⭐⭐⭐⭐ | Rotated/scaled images, different crops |
| **CLIP** | ⚡ | ⭐⭐⭐⭐⭐ | Semantic similarity, different styles |
| **SIFT** | ⚡ | ⭐⭐⭐⭐⭐ | Scale/rotation invariant, highest accuracy |

## Threshold Guidelines

### CLIP Threshold (0.0-1.0)
- **0.95+**: Nearly identical content only
- **0.85-0.94**: Similar subjects/scenes (recommended)
- **0.70-0.84**: Broader semantic similarity
- **<0.70**: Very loose matching

### Hash Threshold (Hamming Distance)
- **5-8**: Very strict structural similarity
- **10-12**: Moderate similarity (recommended)
- **15-20**: Loose structural matching

### ORB/SIFT Threshold (0.0-1.0)
- **0.8+**: High geometric similarity
- **0.6-0.8**: Moderate similarity (recommended)
- **0.4-0.6**: Loose geometric matching

## Performance Tips

1. **For large datasets**: Disable SIFT, use Hash + ORB + CLIP
2. **For accuracy**: Enable all methods with strict thresholds
3. **For speed**: Use Hash only with moderate threshold
4. **For semantic grouping**: Use CLIP only with moderate threshold
5. **GPU acceleration**: Ensure PyTorch uses GPU for CLIP processing

## Troubleshooting

### Common Issues

**"No similarity detection methods available"**
- Install at least one method: `pip install imagehash` or `pip install torch clip-by-openai`

**CLIP fails to load**
- Check PyTorch installation: `python -c "import torch; print(torch.__version__)"`
- Try CPU-only mode: Set device to "cpu" in GUI or `--device cpu` in CLI

**ORB/SIFT errors**
- Install OpenCV: `pip install opencv-python`
- Update OpenCV if using older version

**Memory issues with large images**
- Reduce image resolution before processing
- Process in smaller batches
- Disable SIFT for large datasets

### Performance Optimization

The enhanced version automatically optimizes performance by:
- Using weighted combination of similarity scores
- Parallel feature extraction where possible
- Efficient similarity matrix computation
- Smart threshold adaptation based on active methods

## What's New vs Original

✅ **Added**: Perceptual hashing for structural similarity  
✅ **Added**: ORB feature matching for geometric similarity  
✅ **Added**: Optional SIFT features for highest accuracy  
✅ **Added**: Multi-method combination with weighted scoring  
✅ **Added**: Individual method enable/disable controls  
✅ **Added**: Per-method threshold configuration  
✅ **Added**: Enhanced GUI with method selection  
✅ **Added**: Fallback support when methods are unavailable  
✅ **Improved**: Better accuracy through method combination  
✅ **Improved**: More robust error handling  
✅ **Improved**: Detailed logging of active methods  

The original CLIP-only approach is still available and can be used by disabling other methods.
