#!/usr/bin/env python3
"""
Installation script for CLIP dependencies

This script helps install the required dependencies for CLIP-based image similarity detection.
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {command}")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("Error: Python 3.7 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("Installing CLIP dependencies...")
    print("This may take several minutes depending on your internet connection.\n")
    
    # List of dependencies to install
    dependencies = [
        "torch>=1.9.0",
        "torchvision>=0.10.0", 
        "scikit-learn>=1.0.0",
        "numpy>=1.21.0"
    ]
    
    # Install basic dependencies
    for dep in dependencies:
        if not run_command(f"pip install {dep}"):
            print(f"Failed to install {dep}")
            return False
    
    # Install CLIP from GitHub
    print("\nInstalling CLIP from OpenAI GitHub repository...")
    if not run_command("pip install git+https://github.com/openai/CLIP.git"):
        print("Failed to install CLIP")
        return False
    
    return True

def test_installation():
    """Test if CLIP can be imported successfully"""
    print("\nTesting CLIP installation...")
    try:
        import torch
        import clip
        import sklearn
        import numpy as np
        
        print("✓ All dependencies imported successfully")
        
        # Test CLIP model loading
        print("Testing CLIP model loading...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        model, preprocess = clip.load("ViT-B/32", device=device)
        print(f"✓ CLIP model loaded successfully on {device}")
        
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ CLIP model loading failed: {e}")
        return False

def main():
    print("CLIP Dependencies Installer")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed. Please check the error messages above.")
        sys.exit(1)
    
    # Test installation
    if test_installation():
        print("\n🎉 CLIP installation completed successfully!")
        print("\nYou can now use CLIP-based similarity detection in the Image Variation Finder.")
        print("\nNext steps:")
        print("1. Run the GUI: python image_combiner_gui.py")
        print("2. Or use command line: python auto_variation_finder.py <input_directory>")
    else:
        print("\n⚠️  Installation completed but testing failed.")
        print("You may still be able to use imagehash-based similarity detection.")

if __name__ == "__main__":
    main()