#!/usr/bin/env python3
"""
Test script to verify CLIP model selection functionality
"""

import sys
from auto_variation_finder import ImageVariationFinder

def test_clip_models():
    """Test different CLIP models to ensure they load correctly"""
    
    models_to_test = [
        "ViT-B/32",
        "ViT-L/14",
        "RN50"
    ]
    
    print("Testing CLIP model loading...\n")
    
    for model_name in models_to_test:
        try:
            print(f"Testing {model_name}...")
            finder = ImageVariationFinder(
                clip_threshold=0.85,
                clip_model_name=model_name
            )
            print(f"✓ {model_name} loaded successfully")
            print(f"  Device: {finder.device}")
            print(f"  Model name: {finder.clip_model_name}")
            print()
            
        except Exception as e:
            print(f"✗ {model_name} failed to load: {e}")
            print()
    
    print("Test completed!")

if __name__ == "__main__":
    test_clip_models()