#!/usr/bin/env python3
"""
Test runner for the automatic image variation finder

This script demonstrates the functionality with the test images.
"""

import os
import subprocess
import sys

def run_test():
    """
    Run the automatic variation finder on test images.
    """
    print("=" * 60)
    print("TESTING AUTOMATIC IMAGE VARIATION FINDER")
    print("=" * 60)
    
    # Check if virtual environment exists
    venv_python = "venv/bin/python"
    if not os.path.exists(venv_python):
        print("Error: Virtual environment not found. Please run:")
        print("  python3 -m venv venv")
        print("  source venv/bin/activate")
        print("  pip install -r requirements.txt")
        return False
    
    # Check if test directory exists
    test_dir = "/Users/<USER>/Desktop/ra/test "
    if not os.path.exists(test_dir):
        print(f"Error: Test directory not found: {test_dir}")
        return False
    
    print(f"Found test directory with {len(os.listdir(test_dir))} images")
    
    # Run the automatic variation finder
    print("\nRunning automatic variation detection...")
    
    try:
        result = subprocess.run([
            venv_python,
            "auto_variation_finder.py",
            test_dir,
            "-o", "test_output",
            "--threshold", "15"  # More lenient threshold for test
        ], capture_output=True, text=True, cwd="/Users/<USER>/Desktop/ra")
        
        if result.returncode == 0:
            print("✅ Success! Variation detection completed.")
            print("\nOutput:")
            print(result.stdout)
            
            # List generated files
            output_dir = "/Users/<USER>/Desktop/ra/test_output"
            if os.path.exists(output_dir):
                files = os.listdir(output_dir)
                batch_files = [f for f in files if f.startswith('variation_batch_') and f.endswith('.jpg')]
                source_files = [f for f in files if f.startswith('variation_batch_') and f.endswith('.txt')]
                
                print(f"\n📁 Generated {len(batch_files)} batch images:")
                for f in sorted(batch_files):
                    print(f"  - {f}")
                
                print(f"\n📄 Generated {len(source_files)} source lists:")
                for f in sorted(source_files):
                    print(f"  - {f}")
                
                print(f"\n🎯 All images are combined in a 2x2 grid layout as requested!")
                print(f"\n📂 Check the output directory: {output_dir}")
                
            return True
        else:
            print("❌ Error occurred:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        return False

def show_usage():
    """
    Show usage instructions.
    """
    print("\n" + "=" * 60)
    print("USAGE INSTRUCTIONS")
    print("=" * 60)
    print("\n1. Manual usage:")
    print('   venv/bin/python auto_variation_finder.py "/path/to/images" -o output_dir')
    print("\n2. With custom settings:")
    print('   venv/bin/python auto_variation_finder.py "/path/to/images" --threshold 10 --batch-size 6')
    print("\n3. Test with demo images:")
    print("   venv/bin/python demo_auto_finder.py")
    print("\nFeatures:")
    print("  ✅ Automatic similarity detection using perceptual hashing")
    print("  ✅ Grid layout (2x2) for all batches (as requested)")
    print("  ✅ Batch processing with customizable size")
    print("  ✅ Source file tracking for each batch")
    print("  ✅ Support for multiple image formats")

if __name__ == "__main__":
    success = run_test()
    show_usage()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n⚠️  Test failed. Please check the error messages above.")
        sys.exit(1)