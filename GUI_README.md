# Image Combiner GUI - 8K Output

A user-friendly Tkinter application for automatically finding and combining image variations with high-resolution 8K output support.

## Features

### 🎯 Core Functionality
- **Automatic Image Variation Detection** using perceptual hashing
- **8K Resolution Output** (7680x4320) with support for 4K, 2K, and HD
- **Multiple Layout Modes**: Grid (2x2), Horizontal, and Vertical
- **Batch Processing** with customizable batch sizes
- **Real-time Progress Tracking** with detailed logging

### 🎨 Customization Options
- **Output Resolution**: 8K, 4K, 2K, or HD
- **Layout Modes**: Grid, Horizontal, or Vertical arrangements
- **Similarity Threshold**: Adjustable sensitivity (5-30)
- **Image Spacing**: Customizable gaps between images (0-100px)
- **Background Colors**: White, Black, Gray, or Transparent
- **Output Quality**: JPEG quality control (70-100%)
- **Batch Size**: Process 2-16 images per batch

## Installation

### Prerequisites
- Python 3.7 or higher
- Virtual environment (recommended)

### Setup

1. **Clone or download the project**
   ```bash
   cd /path/to/image-combiner
   ```

2. **Create and activate virtual environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On macOS/Linux
   # or
   venv\Scripts\activate     # On Windows
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Starting the GUI

**Option 1: Direct launch**
```bash
source venv/bin/activate
python image_combiner_gui.py
```

**Option 2: Using launcher script**
```bash
source venv/bin/activate
python launch_gui.py
```

### Using the Application

#### 1. **Select Directories**
   - **Input Directory**: Choose folder containing your images
   - **Output Directory**: Choose where to save combined images

#### 2. **Configure Settings**
   - **Layout Mode**: Choose how images are arranged
     - `Grid`: 2x2 arrangement (recommended for 4 images)
     - `Horizontal`: Side-by-side arrangement
     - `Vertical`: Stacked arrangement
   
   - **Output Resolution**: Select target resolution
     - `8K (7680x4320)`: Ultra-high resolution
     - `4K (3840x2160)`: High resolution
     - `2K (2560x1440)`: Standard high resolution
     - `HD (1920x1080)`: Standard resolution
   
   - **Batch Size**: Number of images per combined output (2-16)
   
   - **Similarity Threshold**: How similar images need to be (5-30)
     - Lower values = more strict matching
     - Higher values = more lenient matching
   
   - **Image Spacing**: Gap between images in pixels (0-100)
   
   - **Background Color**: Color for empty areas
   
   - **Output Quality**: JPEG compression quality (70-100%)

#### 3. **Process Images**
   - Click **"Process Images"** to start
   - Monitor progress in the log window
   - Use **"Open Output Folder"** to view results

### Output Files

For each batch, the application creates:
- `variation_batch_001.jpg` - Combined image at specified resolution
- `variation_batch_001_sources.txt` - List of source images used

## Example Workflows

### Workflow 1: High-Quality Art Prints (8K)
```
Settings:
- Resolution: 8K (7680x4320)
- Layout: Grid
- Batch Size: 4
- Quality: 100%
- Spacing: 50px
- Background: White
```

### Workflow 2: Web Gallery (4K)
```
Settings:
- Resolution: 4K (3840x2160)
- Layout: Horizontal
- Batch Size: 3
- Quality: 90%
- Spacing: 20px
- Background: Black
```

### Workflow 3: Social Media Collage (2K)
```
Settings:
- Resolution: 2K (2560x1440)
- Layout: Grid
- Batch Size: 4
- Quality: 85%
- Spacing: 10px
- Background: Gray
```

## Technical Details

### Image Processing
- **Similarity Detection**: Uses perceptual hashing (pHash) via `imagehash` library
- **Image Loading**: Supports JPEG, PNG, BMP, TIFF, and other PIL-supported formats
- **Resizing**: Maintains aspect ratios with intelligent cropping/padding
- **Color Management**: Automatic RGB conversion for consistency

### Performance
- **Multi-threading**: GUI remains responsive during processing
- **Memory Efficient**: Processes images in batches to manage memory usage
- **Progress Tracking**: Real-time updates and detailed logging

### Output Quality
- **8K Support**: True 8K resolution output (7680x4320)
- **High Quality**: Configurable JPEG quality up to 100%
- **Lossless Options**: PNG output available for transparency

## Troubleshooting

### Common Issues

**GUI doesn't start**
```bash
# Check if virtual environment is activated
source venv/bin/activate

# Verify dependencies
pip list | grep -E "Pillow|imagehash"

# Reinstall if needed
pip install -r requirements.txt
```

**"No images found" error**
- Ensure input directory contains supported image formats
- Check file permissions
- Verify directory path is correct

**Processing fails**
- Check available disk space for 8K output
- Ensure output directory is writable
- Try reducing batch size or resolution

**Memory issues with 8K**
- Reduce batch size to 2-4 images
- Close other applications
- Consider using 4K resolution instead

### Performance Tips

1. **For 8K Output**:
   - Use batch sizes of 2-4 images
   - Ensure 8GB+ RAM available
   - Use SSD storage for faster processing

2. **For Large Collections**:
   - Process in smaller batches
   - Use lower similarity thresholds for better grouping
   - Monitor disk space (8K images can be 10-50MB each)

3. **For Best Quality**:
   - Use 100% quality setting
   - Choose appropriate spacing for your content
   - Consider PNG output for graphics with transparency

## Command Line Alternative

For batch processing or automation, you can still use the command-line version:

```bash
# Basic usage
python auto_variation_finder.py "/path/to/images" -o output_dir

# With custom settings
python auto_variation_finder.py "/path/to/images" \
  --output output_dir \
  --threshold 10 \
  --batch-size 4
```

## File Structure

```
image-combiner/
├── image_combiner_gui.py      # Main GUI application
├── launch_gui.py              # GUI launcher script
├── auto_variation_finder.py   # Core processing logic
├── image_combiner.py          # Image combination utilities
├── requirements.txt           # Python dependencies
├── README.md                  # Main documentation
├── GUI_README.md             # This file
└── venv/                     # Virtual environment
```

## Support

For issues or questions:
1. Check the processing log in the GUI
2. Verify all dependencies are installed
3. Ensure sufficient disk space and memory
4. Try with smaller batch sizes or lower resolutions

---

**Enjoy creating stunning 8K image combinations! 🎨✨**