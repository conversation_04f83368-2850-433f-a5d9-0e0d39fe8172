Requirement already satisfied: torchvision in ./venv/lib/python3.13/site-packages (0.22.1)
Requirement already satisfied: numpy in ./venv/lib/python3.13/site-packages (from torchvision) (2.2.6)
Requirement already satisfied: torch==2.7.1 in ./venv/lib/python3.13/site-packages (from torchvision) (2.7.1)
Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in ./venv/lib/python3.13/site-packages (from torchvision) (11.3.0)
Requirement already satisfied: filelock in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (4.14.1)
Requirement already satisfied: setuptools in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (80.9.0)
Requirement already satisfied: sympy>=1.13.3 in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (1.14.0)
Requirement already satisfied: networkx in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.5)
Requirement already satisfied: jinja2 in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (3.1.6)
Requirement already satisfied: fsspec in ./venv/lib/python3.13/site-packages (from torch==2.7.1->torchvision) (2025.5.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./venv/lib/python3.13/site-packages (from sympy>=1.13.3->torch==2.7.1->torchvision) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in ./venv/lib/python3.13/site-packages (from jinja2->torch==2.7.1->torchvision) (3.0.2)
