#!/usr/bin/env python3
"""
Test script for the Image Combiner GUI

This script tests the GUI components and functionality.
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✅ tkinter imported successfully")
    except ImportError as e:
        print(f"❌ tkinter import failed: {e}")
        return False
    
    try:
        from tkinter import ttk, filedialog, messagebox
        print("✅ tkinter components imported successfully")
    except ImportError as e:
        print(f"❌ tkinter components import failed: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL imported successfully")
    except ImportError as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    try:
        import imagehash
        print("✅ imagehash imported successfully")
    except ImportError as e:
        print(f"❌ imagehash import failed: {e}")
        return False
    
    try:
        from auto_variation_finder import ImageVariationFinder
        print("✅ ImageVariationFinder imported successfully")
    except ImportError as e:
        print(f"❌ ImageVariationFinder import failed: {e}")
        return False
    
    try:
        from image_combiner import ImageCombiner
        print("✅ ImageCombiner imported successfully")
    except ImportError as e:
        print(f"❌ ImageCombiner import failed: {e}")
        return False
    
    return True

def test_gui_creation():
    """Test if GUI can be created without errors."""
    print("\nTesting GUI creation...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create a test window
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("400x300")
        
        # Test basic widgets
        frame = ttk.Frame(root, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        label = ttk.Label(frame, text="Test Label")
        label.pack(pady=5)
        
        entry = ttk.Entry(frame)
        entry.pack(pady=5)
        
        button = ttk.Button(frame, text="Test Button")
        button.pack(pady=5)
        
        # Test variables
        string_var = tk.StringVar(value="test")
        int_var = tk.IntVar(value=42)
        
        print("✅ Basic GUI components created successfully")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False

def test_image_processing():
    """Test basic image processing functionality."""
    print("\nTesting image processing...")
    
    try:
        from PIL import Image
        import imagehash
        
        # Create a test image
        test_img = Image.new('RGB', (100, 100), color='red')
        print("✅ Test image created")
        
        # Test hash calculation
        hash_val = imagehash.phash(test_img)
        print(f"✅ Image hash calculated: {hash_val}")
        
        # Test image combiner
        from image_combiner import ImageCombiner
        combiner = ImageCombiner()
        
        # Test resizing
        resized = combiner.resize_images_uniform([test_img], target_size=(50, 50))
        print("✅ Image resizing works")
        
        # Test combining
        combined = combiner.combine_horizontal(resized)
        print(f"✅ Image combining works: {combined.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Image processing test failed: {e}")
        return False

def test_file_operations():
    """Test file operations."""
    print("\nTesting file operations...")
    
    try:
        import tempfile
        import os
        
        # Test directory creation
        with tempfile.TemporaryDirectory() as temp_dir:
            test_subdir = os.path.join(temp_dir, "test_output")
            os.makedirs(test_subdir, exist_ok=True)
            print("✅ Directory creation works")
            
            # Test file writing
            test_file = os.path.join(test_subdir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("Test content")
            print("✅ File writing works")
            
            # Test file reading
            with open(test_file, 'r') as f:
                content = f.read()
            print(f"✅ File reading works: '{content}'")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("IMAGE COMBINER GUI - COMPONENT TESTS")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("GUI Creation Tests", test_gui_creation),
        ("Image Processing Tests", test_image_processing),
        ("File Operations Tests", test_file_operations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The GUI should work correctly.")
        print("\nTo start the GUI, run:")
        print("  python image_combiner_gui.py")
        print("  or")
        print("  python launch_gui.py")
    else:
        print("\n⚠️  Some tests failed. Please check the error messages above.")
        print("\nMake sure you have:")
        print("  1. Activated the virtual environment: source venv/bin/activate")
        print("  2. Installed dependencies: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)