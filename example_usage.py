#!/usr/bin/env python3
"""
Example usage of the ImageCombiner class

This script demonstrates how to use the ImageCombiner programmatically
rather than through the command line interface.
"""

from image_combiner import ImageCombiner
from PIL import Image
import os

def create_sample_images():
    """
    Create some sample images for demonstration.
    This creates colored rectangles as sample images.
    """
    colors = ['red', 'green', 'blue', 'yellow', 'purple', 'orange']
    sample_dir = 'sample_images'
    
    if not os.path.exists(sample_dir):
        os.makedirs(sample_dir)
    
    image_paths = []
    for i, color in enumerate(colors):
        # Create a colored rectangle
        img = Image.new('RGB', (200, 150), color)
        path = os.path.join(sample_dir, f'sample_{i+1}_{color}.png')
        img.save(path)
        image_paths.append(path)
        print(f"Created sample image: {path}")
    
    return image_paths

def example_grid_layout():
    """
    Example: Create a grid layout
    """
    print("\n=== Grid Layout Example ===")
    
    # Create sample images
    image_paths = create_sample_images()
    
    # Initialize the combiner
    combiner = ImageCombiner(spacing=15, background_color='lightgray')
    
    # Load images
    images = combiner.load_images(image_paths)
    
    # Resize to uniform size
    images = combiner.resize_images_uniform(images, target_size=(150, 150))
    
    # Combine in a 3-column grid
    combined = combiner.combine_grid(images, cols=3)
    
    # Save the result
    output_path = 'example_grid.png'
    combined.save(output_path)
    print(f"Grid layout saved as: {output_path}")
    print(f"Dimensions: {combined.size}")

def example_horizontal_layout():
    """
    Example: Create a horizontal layout
    """
    print("\n=== Horizontal Layout Example ===")
    
    # Create sample images
    image_paths = create_sample_images()[:4]  # Use only first 4 images
    
    # Initialize the combiner
    combiner = ImageCombiner(spacing=20, background_color='white')
    
    # Load images
    images = combiner.load_images(image_paths)
    
    # Resize to uniform size
    images = combiner.resize_images_uniform(images, target_size=(120, 120))
    
    # Combine horizontally
    combined = combiner.combine_horizontal(images)
    
    # Save the result
    output_path = 'example_horizontal.png'
    combined.save(output_path)
    print(f"Horizontal layout saved as: {output_path}")
    print(f"Dimensions: {combined.size}")

def example_vertical_layout():
    """
    Example: Create a vertical layout
    """
    print("\n=== Vertical Layout Example ===")
    
    # Create sample images
    image_paths = create_sample_images()[:3]  # Use only first 3 images
    
    # Initialize the combiner
    combiner = ImageCombiner(spacing=25, background_color='black')
    
    # Load images
    images = combiner.load_images(image_paths)
    
    # Resize to uniform size
    images = combiner.resize_images_uniform(images, target_size=(200, 100))
    
    # Combine vertically
    combined = combiner.combine_vertical(images)
    
    # Save the result
    output_path = 'example_vertical.png'
    combined.save(output_path)
    print(f"Vertical layout saved as: {output_path}")
    print(f"Dimensions: {combined.size}")

def example_custom_usage():
    """
    Example: Custom usage with different settings
    """
    print("\n=== Custom Usage Example ===")
    
    # Create sample images
    image_paths = create_sample_images()
    
    # Initialize the combiner with custom settings
    combiner = ImageCombiner(spacing=30, background_color='#2C3E50')
    
    # Load images
    images = combiner.load_images(image_paths)
    
    # Don't resize - keep original proportions but create a grid
    combined = combiner.combine_grid(images, cols=2)
    
    # Save the result
    output_path = 'example_custom.png'
    combined.save(output_path)
    print(f"Custom layout saved as: {output_path}")
    print(f"Dimensions: {combined.size}")

def cleanup_samples():
    """
    Clean up sample images
    """
    import shutil
    sample_dir = 'sample_images'
    if os.path.exists(sample_dir):
        shutil.rmtree(sample_dir)
        print(f"\nCleaned up {sample_dir} directory")

if __name__ == "__main__":
    print("Image Combiner - Example Usage")
    print("=" * 40)
    
    try:
        # Run examples
        example_grid_layout()
        example_horizontal_layout()
        example_vertical_layout()
        example_custom_usage()
        
        print("\n=== All Examples Complete ===")
        print("Check the generated files:")
        print("- example_grid.png")
        print("- example_horizontal.png")
        print("- example_vertical.png")
        print("- example_custom.png")
        
    except Exception as e:
        print(f"Error running examples: {e}")
    
    finally:
        # Clean up sample images
        cleanup_samples()