Requirement already satisfied: torch in ./venv/lib/python3.13/site-packages (2.7.1)
Requirement already satisfied: filelock in ./venv/lib/python3.13/site-packages (from torch) (3.18.0)
Requirement already satisfied: typing-extensions>=4.10.0 in ./venv/lib/python3.13/site-packages (from torch) (4.14.1)
Requirement already satisfied: setuptools in ./venv/lib/python3.13/site-packages (from torch) (80.9.0)
Requirement already satisfied: sympy>=1.13.3 in ./venv/lib/python3.13/site-packages (from torch) (1.14.0)
Requirement already satisfied: networkx in ./venv/lib/python3.13/site-packages (from torch) (3.5)
Requirement already satisfied: jinja2 in ./venv/lib/python3.13/site-packages (from torch) (3.1.6)
Requirement already satisfied: fsspec in ./venv/lib/python3.13/site-packages (from torch) (2025.5.1)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./venv/lib/python3.13/site-packages (from sympy>=1.13.3->torch) (1.3.0)
Requirement already satisfied: MarkupSafe>=2.0 in ./venv/lib/python3.13/site-packages (from jinja2->torch) (3.0.2)
