# Enhanced Image Variation Finder Requirements
# Core dependencies
Pillow>=9.0.0
numpy>=1.21.0
opencv-python>=4.5.0

# CLIP for semantic similarity (optional but recommended)
torch>=1.9.0
torchvision>=0.10.0
git+https://github.com/openai/CLIP.git
scikit-learn>=1.0.0

# Perceptual hashing for structural similarity (optional but recommended)
imagehash>=4.3.0

# Installation instructions:
#
# For basic functionality (perceptual hashing + ORB/SIFT):
# pip install Pillow numpy opencv-python imagehash
#
# For full functionality including CLIP:
# pip install -r requirements.txt
#
# Note: CLIP requires PyTorch. If you have GPU support, install PyTorch with CUDA:
# pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118